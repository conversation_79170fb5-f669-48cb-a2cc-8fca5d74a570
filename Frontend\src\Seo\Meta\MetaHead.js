import JsonLdSchema from "@/Seo/Schema/JsonLdSchema";
import Head from "next/head";

export default function MetaHead({ props, children, schemas }) {
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
  
  const defaultMeta = {
    title: "TradeReply: Optimize Your Trading Strategies & Analytics",
    description:
      "Optimize your trades with TradeReply.com. Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success.",
    canonical: "https://www.tradereply.com/",
    ogTitle: "TradeReply: Optimize Your Trading Strategies & Analytics",
    ogDescription:
      "Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.",
    ogSiteName: "TradeReply",
    ogImage:
      "https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg",
    twitterTitle: "TradeReply: Optimize Your Trading Strategies & Analytics",
    twitterDescription:
      "Access powerful trading strategies, real-time analytics, and tools for crypto and stock market success with TradeReply.com.",
    twitterImage:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
  };
  const isNoIndex = props?.noindex === true;
  const robotsContent =
    props?.robots || (isNoIndex ? "noindex, follow" : "index, follow");

  return (
    <>
      <Head>
        {children}
        <title>{props?.title || defaultMeta.title}</title>

        {/* Always render robots tag */}
        {/* <meta
        name="robots"
        content={isNoIndex ? "noindex, follow" : "index, follow"}
      /> */}
        <meta name="robots" content={robotsContent} />
        {props?.canonical_link?.trim() && props?.noindex !== true && (
          <link rel="canonical" href={props.canonical_link} />
        )}

        <meta
          name="description"
          content={props?.description || defaultMeta.description}
        />

        {props?.rel_next && <link rel="next" href={props?.rel_next} />}

        <meta
          property="og:title"
          content={props?.og_title || defaultMeta?.ogTitle}
        />
        <meta
          property="og:description"
          content={props?.og_description || defaultMeta?.ogDescription}
        />
        <meta
          property="og:site_name"
          content={props?.og_site_name || defaultMeta?.ogSiteName}
        />

        <meta
          property="og:image"
          content="https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png"
        />
        <meta property="og:type" content="website" />

        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:locale" content="en_US" />
        {schemas && <JsonLdSchema schemas={schemas} />}
        {/*/!* Twitter Meta Tags *!/*/}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content={props?.twitter_title || defaultMeta?.twitterTitle}
        />
        <meta
          name="twitter:description"
          content={
            props?.twitter_description || defaultMeta?.twitterDescription
          }
        />
        <meta
          name="twitter:image"
          content="https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png"
        />
        <meta name="twitter:site" content="@JoinTradeReply" />

        {/* Favicon */}
        <link
          rel="icon"
          type="image/x-icon"
          href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`}
        />
        <link
          rel="icon"
          type="image/svg+xml"
          href={`https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`}
        />

        
      </Head>
    </>
  );
}
